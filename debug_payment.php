<?php
// Debug script for admin payment issues
// This script will help us test the parameter decryption

// Test parameter from URL: YXhPczIzV0RJTEliUVB4bUx0V0VMQT09
$encrypted_param = 'YXhPczIzV0RJTEliUVB4bUx0V0VMQT09';

echo "=== Admin Payment Debug Script ===\n\n";

echo "1. Testing parameter decryption:\n";
echo "Encrypted parameter: " . $encrypted_param . "\n";

// Implement the passwordChanger function from Mdb.php
function passwordChanger($action, $string, $add_to_secret_key="") {
    if (is_array($string)){
        $result = array();
        foreach($string AS $key=>$val)
            $result[$key] = passwordChanger($action, $val,$add_to_secret_key);
        return $result;
    } else {
        $output         = false;
        $encrypt_method     = "AES-256-CBC";
        $fixed_key = '123456'; // Fixed component for consistency
        $secret_key     = $fixed_key.'0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'.$add_to_secret_key;
        $domain = $_SERVER['SERVER_NAME'] ?? 'localhost';
        $secret_iv          = 'sks'.$domain;
        $key                = hash('sha256', $secret_key);
        $iv             = substr(hash('sha256', $secret_iv), 0, 16);
        if( $action == 'encrypt' ) {
            $output     = openssl_encrypt($string, $encrypt_method, $key, 0, $iv);
            $output     = base64_encode($output);
            $eski           = array("+","/","=");
            $yeni           = array("b1X4","x8V7","F3h7");
            $output     = str_replace($eski,$yeni,$output);
        }elseif( $action == 'decrypt' ){
            $eski=array("b1X4","x8V7","F3h7");
            $yeni=array("+","/","=");
            $string = str_replace($eski,$yeni,$string);
            $output = openssl_decrypt(base64_decode($string), $encrypt_method, $key, 0, $iv);
        }
        return $output;
    }
}

try {
    // Test decryption
    $decrypted_id = passwordChanger('decrypt', $encrypted_param);
    echo "Decrypted ID: " . $decrypted_id . "\n";

    if (empty($decrypted_id)) {
        echo "ERROR: Decryption failed or returned empty result\n";

        // Try simple base64 decode
        echo "\n2. Testing simple base64 decode:\n";
        $simple_decode = base64_decode($encrypted_param);
        echo "Simple base64 decode: " . $simple_decode . "\n";

        // Try double base64 decode
        $double_decode = base64_decode($simple_decode);
        echo "Double base64 decode: " . $double_decode . "\n";

    } else {
        echo "SUCCESS: Decryption successful\n";
        echo "Decrypted value: '" . $decrypted_id . "'\n";
        echo "Is numeric: " . (is_numeric($decrypted_id) ? 'Yes' : 'No') . "\n";
    }

    // Test encryption/decryption with a known value
    echo "\n3. Testing encryption/decryption with known value:\n";
    $test_id = "123"; // Test with ID 123
    $encrypted_test = passwordChanger('encrypt', $test_id);
    echo "Test ID: " . $test_id . "\n";
    echo "Encrypted: " . $encrypted_test . "\n";

    $decrypted_test = passwordChanger('decrypt', $encrypted_test);
    echo "Decrypted back: " . $decrypted_test . "\n";
    echo "Match: " . ($test_id === $decrypted_test ? 'YES' : 'NO') . "\n";

} catch (Exception $e) {
    echo "ERROR: Exception occurred: " . $e->getMessage() . "\n";
}

echo "\n=== Debug Complete ===\n";
?>
