<?php
// Test script to check resellers in database
// This will help us find a valid reseller ID to test with

// Database configuration (adjust as needed)
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'allsimoffer'; // Adjust database name as needed

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== Reseller Database Test ===\n\n";
    
    // Get first 5 resellers
    $stmt = $pdo->query("SELECT id, username, custype, balance, bank_balance, drive_bal, p_id FROM reseller LIMIT 5");
    $resellers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($resellers)) {
        echo "No resellers found in database\n";
    } else {
        echo "Found " . count($resellers) . " resellers:\n\n";
        
        foreach ($resellers as $reseller) {
            echo "ID: " . $reseller['id'] . "\n";
            echo "Username: " . $reseller['username'] . "\n";
            echo "Customer Type: " . $reseller['custype'] . "\n";
            echo "Balance: " . $reseller['balance'] . "\n";
            echo "Bank Balance: " . $reseller['bank_balance'] . "\n";
            echo "Drive Balance: " . $reseller['drive_bal'] . "\n";
            echo "Parent ID: " . $reseller['p_id'] . "\n";
            
            // Get parent name
            if ($reseller['p_id'] == 0) {
                $parent = "Admin";
            } else {
                $parent_stmt = $pdo->prepare("SELECT username FROM reseller WHERE id = ?");
                $parent_stmt->execute([$reseller['p_id']]);
                $parent_row = $parent_stmt->fetch(PDO::FETCH_ASSOC);
                $parent = $parent_row ? $parent_row['username'] : "Unknown Parent";
            }
            echo "Parent: " . $parent . "\n";
            echo "---\n";
        }
        
        // Test encryption with demo1 reseller ID (has balance)
        if (!empty($resellers)) {
            $test_id = 213403; // demo1 reseller with 500 balance
            echo "\nTesting encryption with ID: " . $test_id . "\n";
            
            // Implement the fixed passwordChanger function
            function passwordChanger($action, $string, $add_to_secret_key="") {
                $output = false;
                $encrypt_method = "AES-256-CBC";
                $fixed_key = '123456'; // Fixed component for consistency
                $secret_key = $fixed_key.'0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'.$add_to_secret_key;
                $domain = $_SERVER['SERVER_NAME'] ?? 'localhost';
                $secret_iv = 'sks'.$domain;
                $key = hash('sha256', $secret_key);
                $iv = substr(hash('sha256', $secret_iv), 0, 16);
                
                if( $action == 'encrypt' ) {
                    $output = openssl_encrypt($string, $encrypt_method, $key, 0, $iv);
                    $output = base64_encode($output);
                    $eski = array("+","/","=");
                    $yeni = array("b1X4","x8V7","F3h7");
                    $output = str_replace($eski,$yeni,$output);
                } elseif( $action == 'decrypt' ){
                    $eski = array("b1X4","x8V7","F3h7");
                    $yeni = array("+","/","=");
                    $string = str_replace($eski,$yeni,$string);
                    $output = openssl_decrypt(base64_decode($string), $encrypt_method, $key, 0, $iv);
                }
                return $output;
            }
            
            $encrypted = passwordChanger('encrypt', $test_id);
            echo "Encrypted: " . $encrypted . "\n";
            
            $decrypted = passwordChanger('decrypt', $encrypted);
            echo "Decrypted: " . $decrypted . "\n";
            echo "Match: " . ($test_id == $decrypted ? 'YES' : 'NO') . "\n";
            
            echo "\nTest URL: http://192.168.0.106/AllSimOffer/admin/adminPayment/" . $encrypted . "\n";
        }
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
    echo "Please check your database configuration\n";
}

echo "\n=== Test Complete ===\n";
?>
